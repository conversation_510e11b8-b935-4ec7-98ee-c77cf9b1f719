const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 验证时间格式的函数
function validateTimeFormat(timeString, fieldName) {
  // 检查是否为 YYYY-MM-DD HH:mm:ss 格式
  const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
  
  if (!timeString) {
    console.log(`⚠️  ${fieldName}: null (可能是正常情况)`);
    return true;
  }
  
  if (timeRegex.test(timeString)) {
    console.log(`✅ ${fieldName}: ${timeString} (格式正确)`);
    return true;
  } else {
    console.log(`❌ ${fieldName}: ${timeString} (格式错误，应为 YYYY-MM-DD HH:mm:ss)`);
    return false;
  }
}

// 测试函数
async function testTimeFormat() {
  console.log('🧪 开始测试时间格式统一（dayjs格式）...\n');

  try {
    // 1. 测试对象形式请求
    console.log('📋 测试1: 对象形式请求');
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 5.000,
        tempMilk: {
          add: 10.000,
          reduce: 3.000
        }
      });
      
      console.log('✅ 请求成功');
      
      if (response1.data && response1.data.data) {
        const data = response1.data.data;
        
        console.log('\n🕐 时间格式验证：');
        let allValid = true;
        
        // 验证 beforeUpdate.lastActiveTime
        allValid &= validateTimeFormat(data.beforeUpdate?.lastActiveTime, 'beforeUpdate.lastActiveTime');
        
        // 验证 afterUpdate.lastActiveTime
        allValid &= validateTimeFormat(data.afterUpdate?.lastActiveTime, 'afterUpdate.lastActiveTime');
        
        // 验证 timestamp
        allValid &= validateTimeFormat(data.timestamp, 'timestamp');
        
        if (allValid) {
          console.log('\n🎉 所有时间格式都正确！');
        } else {
          console.log('\n⚠️  存在时间格式问题');
        }
        
        console.log('\n📊 完整响应数据:');
        console.log(JSON.stringify(response1.data, null, 2));
      } else {
        console.log('❌ 响应数据结构异常');
      }
      
      console.log('');
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待6秒避免防刷保护
    console.log('⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试无参数调用
    console.log('📋 测试2: 无参数调用');
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', {});
      
      console.log('✅ 无参数请求成功');
      
      if (response2.data && response2.data.data) {
        const data = response2.data.data;
        
        console.log('\n🕐 时间格式验证：');
        let allValid = true;
        
        // 验证时间格式
        allValid &= validateTimeFormat(data.beforeUpdate?.lastActiveTime, 'beforeUpdate.lastActiveTime');
        allValid &= validateTimeFormat(data.afterUpdate?.lastActiveTime, 'afterUpdate.lastActiveTime');
        allValid &= validateTimeFormat(data.timestamp, 'timestamp');
        
        if (allValid) {
          console.log('\n🎉 所有时间格式都正确！');
        } else {
          console.log('\n⚠️  存在时间格式问题');
        }
        
        console.log('\n📊 完整响应数据:');
        console.log(JSON.stringify(response2.data, null, 2));
      } else {
        console.log('❌ 响应数据结构异常');
      }
      
      console.log('');
    } catch (error) {
      console.log('❌ 无参数请求失败:', error.response?.data || error.message);
      console.log('');
    }

    // 等待6秒避免防刷保护
    console.log('⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试只提供GEM
    console.log('📋 测试3: 只提供GEM');
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 8.000
      });
      
      console.log('✅ 只提供GEM请求成功');
      
      if (response3.data && response3.data.data) {
        const data = response3.data.data;
        
        console.log('\n🕐 时间格式验证：');
        let allValid = true;
        
        // 验证时间格式
        allValid &= validateTimeFormat(data.beforeUpdate?.lastActiveTime, 'beforeUpdate.lastActiveTime');
        allValid &= validateTimeFormat(data.afterUpdate?.lastActiveTime, 'afterUpdate.lastActiveTime');
        allValid &= validateTimeFormat(data.timestamp, 'timestamp');
        
        if (allValid) {
          console.log('\n🎉 所有时间格式都正确！');
        } else {
          console.log('\n⚠️  存在时间格式问题');
        }
        
        // 只显示时间相关字段
        console.log('\n📊 时间相关字段:');
        console.log({
          beforeUpdate: {
            lastActiveTime: data.beforeUpdate?.lastActiveTime
          },
          afterUpdate: {
            lastActiveTime: data.afterUpdate?.lastActiveTime
          },
          timestamp: data.timestamp
        });
      } else {
        console.log('❌ 响应数据结构异常');
      }
      
      console.log('');
    } catch (error) {
      console.log('❌ 只提供GEM请求失败:', error.response?.data || error.message);
      console.log('');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('🎯 时间格式测试完成！');
  console.log('');
  console.log('📝 期望的时间格式：YYYY-MM-DD HH:mm:ss');
  console.log('📝 例如：2025-06-21 16:30:45');
}

// 运行测试
testTimeFormat();
