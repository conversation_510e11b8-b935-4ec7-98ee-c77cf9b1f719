const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析响应数据的函数
function analyzeResponse(response, testName) {
  console.log(`\n📊 ${testName} - 响应分析:`);
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析GEM变化
    const gemBefore = data.beforeUpdate?.gem || 0;
    const gemAfter = data.afterUpdate?.gem || 0;
    const gemActualChange = gemAfter - gemBefore;
    
    console.log(`💎 GEM变化:`);
    console.log(`   更新前: ${gemBefore}`);
    console.log(`   更新后: ${gemAfter}`);
    console.log(`   实际变化: ${gemActualChange}`);
    console.log(`   报告的gemAdded: ${data.changes?.gemAdded || 0}`);
    
    // 分析牛奶变化
    const milkBefore = data.beforeUpdate?.pendingMilk || 0;
    const milkAfter = data.afterUpdate?.pendingMilk || 0;
    const milkActualChange = milkAfter - milkBefore;
    
    console.log(`🥛 牛奶变化:`);
    console.log(`   更新前: ${milkBefore}`);
    console.log(`   更新后: ${milkAfter}`);
    console.log(`   实际变化: ${milkActualChange}`);
    console.log(`   报告的pendingMilkAdded: ${data.changes?.pendingMilkAdded || 0}`);
    
    // 分析详细信息
    if (data.changes?.details) {
      console.log(`📋 详细增减信息:`);
      console.log(`   GEM增加: ${data.changes.details.gem?.increased || 0}`);
      console.log(`   GEM减少: ${data.changes.details.gem?.decreased || 0}`);
      console.log(`   牛奶增加: ${data.changes.details.milk?.increased || 0}`);
      console.log(`   牛奶减少: ${data.changes.details.milk?.decreased || 0}`);
    } else {
      console.log(`⚠️  缺少详细增减信息`);
    }
    
    // 检查是否使用了系统计算
    if (data.changes?.usedSystemCalculation) {
      console.log(`⚙️  使用了系统计算: ${data.changes.systemCalculationReason}`);
    }
    
    // 验证数据一致性
    console.log(`\n🔍 数据一致性检查:`);
    if (Math.abs(gemActualChange - (data.changes?.gemAdded || 0)) < 0.001) {
      console.log(`   ✅ GEM变化数据一致`);
    } else {
      console.log(`   ❌ GEM变化数据不一致`);
    }
    
    if (Math.abs(milkActualChange - (data.changes?.pendingMilkAdded || 0)) < 0.001) {
      console.log(`   ✅ 牛奶变化数据一致`);
    } else {
      console.log(`   ❌ 牛奶变化数据不一致`);
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testGemIncreaseAndDetails() {
  console.log('🧪 开始测试GEM增加和详细增减信息...\n');

  try {
    // 1. 测试你提供的参数
    console.log('📋 测试1: 你提供的参数');
    console.log('请求参数:', JSON.stringify({
      tempGem: 100,
      tempMilk: {
        add: 100,
        reduce: 100
      }
    }, null, 2));
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 100,
        tempMilk: {
          add: 100,
          reduce: 100
        }
      });
      
      console.log('✅ 请求成功');
      analyzeResponse(response1, '测试1');
      
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试只增加GEM
    console.log('\n📋 测试2: 只增加GEM');
    console.log('请求参数:', JSON.stringify({
      tempGem: 50
    }, null, 2));
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', {
        tempGem: 50
      });
      
      console.log('✅ 请求成功');
      analyzeResponse(response2, '测试2');
      
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试只增加牛奶
    console.log('\n📋 测试3: 只增加牛奶');
    console.log('请求参数:', JSON.stringify({
      tempMilk: {
        add: 75
      }
    }, null, 2));
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', {
        tempMilk: {
          add: 75
        }
      });
      
      console.log('✅ 请求成功');
      analyzeResponse(response3, '测试3');
      
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 4. 测试无参数调用
    console.log('\n📋 测试4: 无参数调用（系统自动计算）');
    console.log('请求参数:', JSON.stringify({}, null, 2));
    
    try {
      const response4 = await api.post('/api/wallet/batch-update-resources', {});
      
      console.log('✅ 请求成功');
      analyzeResponse(response4, '测试4');
      
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 测试总结：');
  console.log('✅ 检查GEM是否正确增加');
  console.log('✅ 验证详细增减信息是否正确');
  console.log('✅ 确认数据一致性');
  console.log('✅ 测试不同参数组合');
}

// 运行测试
testGemIncreaseAndDetails();
