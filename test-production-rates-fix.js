const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析生产速率的函数
function analyzeProductionRates(response, testName, requestData) {
  console.log(`\n📊 ${testName} - 生产速率修复验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    console.log(`   API报告时间: ${data.changes?.productionRates?.timeElapsedSeconds || 'N/A'} 秒`);
    
    // 分析生产速率
    if (data.changes?.productionRates) {
      const rates = data.changes.productionRates;
      console.log(`🏭 API报告的生产速率:`);
      console.log(`   农场牛奶生产: ${rates.farmMilkPerSecond || rates.milkPerSecond || 'N/A'} 牛奶/秒`);
      console.log(`   出货线牛奶消耗: ${rates.deliveryMilkPerSecond || 'N/A'} 牛奶/秒`);
      console.log(`   出货线GEM生产: ${rates.gemPerSecond} GEM/秒`);
      
      // 基于你提供的出货线配置验证
      console.log(`🔧 出货线配置验证:`);
      console.log(`   出货速度: 5秒`);
      console.log(`   方块单位: 5牛奶`);
      console.log(`   方块价格: 5GEM`);
      
      // 正确的计算
      const correctMilkConsumptionPerSecond = 5 / 5; // 5牛奶 ÷ 5秒 = 1牛奶/秒
      const correctGemPerSecond = 5 / 5;  // 5GEM ÷ 5秒 = 1GEM/秒
      
      console.log(`📈 正确的出货线速率应该是:`);
      console.log(`   牛奶消耗速率: ${correctMilkConsumptionPerSecond} 牛奶/秒`);
      console.log(`   GEM生产速率: ${correctGemPerSecond} GEM/秒`);
      
      // 验证修复
      console.log(`🔍 速率修复验证:`);
      if (Math.abs(rates.gemPerSecond - correctGemPerSecond) < 0.1) {
        console.log(`   ✅ GEM生产速率计算正确 (${rates.gemPerSecond} ≈ ${correctGemPerSecond})`);
      } else {
        console.log(`   ❌ GEM生产速率计算错误 (${rates.gemPerSecond} ≠ ${correctGemPerSecond})`);
      }
      
      if (rates.deliveryMilkPerSecond !== undefined) {
        if (Math.abs(rates.deliveryMilkPerSecond - correctMilkConsumptionPerSecond) < 0.1) {
          console.log(`   ✅ 牛奶消耗速率计算正确 (${rates.deliveryMilkPerSecond} ≈ ${correctMilkConsumptionPerSecond})`);
        } else {
          console.log(`   ❌ 牛奶消耗速率计算错误 (${rates.deliveryMilkPerSecond} ≠ ${correctMilkConsumptionPerSecond})`);
        }
      } else {
        console.log(`   ⚠️  缺少deliveryMilkPerSecond字段`);
      }
      
      // 计算理论值
      const timeSeconds = rates.timeElapsedSeconds;
      const farmMilkPerSecond = rates.farmMilkPerSecond || rates.milkPerSecond || 0;
      
      const expectedFarmMilkProduction = farmMilkPerSecond * timeSeconds;
      const expectedMilkConsumption = correctMilkConsumptionPerSecond * timeSeconds;
      const expectedGemProduction = correctGemPerSecond * timeSeconds;
      
      console.log(`📊 基于正确速率的理论计算:`);
      console.log(`   理论农场牛奶生产: ${expectedFarmMilkProduction.toFixed(3)}`);
      console.log(`   理论出货线牛奶消耗: ${expectedMilkConsumption.toFixed(3)}`);
      console.log(`   理论GEM生产: ${expectedGemProduction.toFixed(3)}`);
      
      // 分析实际返回值
      if (data.changes?.details) {
        const details = data.changes.details;
        console.log(`📋 API返回的details值:`);
        console.log(`   gem.increased: ${details.gem?.increased || 0}`);
        console.log(`   milk.increased: ${details.milk?.increased || 0}`);
        console.log(`   milk.decreased: ${details.milk?.decreased || 0}`);
        
        // 验证details是否基于生产速率计算
        console.log(`🎯 details修复验证:`);
        
        // 检查gem.increased是否基于GEM生产速率
        if (details.gem?.increased !== undefined) {
          if (Math.abs(details.gem.increased - expectedGemProduction) < 0.1) {
            console.log(`   ✅ gem.increased 基于GEM生产速率计算正确`);
          } else {
            console.log(`   ❌ gem.increased 未基于GEM生产速率计算 (${details.gem.increased} ≠ ${expectedGemProduction.toFixed(3)})`);
          }
        }
        
        // 检查milk.increased是否基于农场生产速率
        if (details.milk?.increased !== undefined) {
          if (Math.abs(details.milk.increased - expectedFarmMilkProduction) < 0.1) {
            console.log(`   ✅ milk.increased 基于农场生产速率计算正确`);
          } else {
            console.log(`   ❌ milk.increased 未基于农场生产速率计算 (${details.milk.increased} ≠ ${expectedFarmMilkProduction.toFixed(3)})`);
          }
        }
        
        // 检查milk.decreased是否基于出货线消耗速率
        if (details.milk?.decreased !== undefined) {
          if (Math.abs(details.milk.decreased - expectedMilkConsumption) < 0.1) {
            console.log(`   ✅ milk.decreased 基于出货线消耗速率计算正确`);
          } else {
            console.log(`   ❌ milk.decreased 未基于出货线消耗速率计算 (${details.milk.decreased} ≠ ${expectedMilkConsumption.toFixed(3)})`);
          }
        }
        
        // 检查是否还在显示前端请求值
        if (requestData.tempMilk) {
          const frontendAdd = requestData.tempMilk.add || 0;
          const frontendReduce = requestData.tempMilk.reduce || 0;
          
          if (details.milk.increased === frontendAdd && details.milk.decreased === frontendReduce) {
            console.log(`   ❌ 仍在显示前端请求值，未使用生产速率计算`);
          } else {
            console.log(`   ✅ 已使用生产速率计算，不再显示前端请求值`);
          }
        }
        
        if (requestData.tempGem && details.gem?.increased === requestData.tempGem) {
          // 这种情况可能是合理的，如果前端请求在1.5倍范围内
          console.log(`   📝 gem.increased 等于前端请求值，可能是合理的（在1.5倍范围内）`);
        }
      }
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testProductionRatesFix() {
  console.log('🧪 开始测试生产速率修复...\n');
  console.log('🎯 修复目标:');
  console.log('1. milk.increased = 基于农场生产速率计算');
  console.log('2. milk.decreased = 基于出货线消耗速率计算');
  console.log('3. gem.increased = 基于GEM生产速率计算');
  console.log('4. 不再显示前端请求的原始值');

  try {
    // 1. 测试你的原始请求
    console.log('\n📋 测试1: 你的原始请求（净值为0）');
    const request1 = {
      tempGem: 100,
      tempMilk: {
        add: 100,
        reduce: 100
      }
    };
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeProductionRates(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试有净值的请求
    console.log('\n📋 测试2: 有净值的牛奶请求');
    const request2 = {
      tempGem: 50,
      tempMilk: {
        add: 80,
        reduce: 60
      }
    };
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeProductionRates(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试只有GEM的请求
    console.log('\n📋 测试3: 只有GEM的请求');
    const request3 = {
      tempGem: 30
    };
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeProductionRates(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 期望的修复效果：');
  console.log('✅ details.gem.increased 基于GEM生产速率计算');
  console.log('✅ details.milk.increased 基于农场生产速率计算');
  console.log('✅ details.milk.decreased 基于出货线消耗速率计算');
  console.log('✅ 所有值都使用BigNumber保持3位小数精度');
}

// 运行测试
testProductionRatesFix();
