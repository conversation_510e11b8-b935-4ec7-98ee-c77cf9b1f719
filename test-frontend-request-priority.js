const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析前端请求优先级的函数
function analyzeFrontendRequestPriority(response, testName, requestData) {
  console.log(`\n📊 ${testName} - 前端请求优先级验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    
    // 基于配置计算理论值
    const farmCycles = Math.floor(actualTimeDiff / 5); // 农场周期数
    const deliveryCycles = Math.floor(actualTimeDiff / 5); // 出货线周期数
    
    const theoreticalFarmMilk = farmCycles * 1; // 每周期1牛奶
    const theoreticalDeliveryMilk = deliveryCycles * 5; // 每周期消耗5牛奶
    const theoreticalGems = deliveryCycles * 5; // 每周期产5GEM
    
    console.log(`📈 系统理论计算:`);
    console.log(`   理论农场产量: ${theoreticalFarmMilk} 牛奶`);
    console.log(`   理论出货线消耗: ${theoreticalDeliveryMilk} 牛奶`);
    console.log(`   理论GEM产量: ${theoreticalGems} GEM`);
    
    // 分析1.5倍范围
    const gemMax = theoreticalGems * 1.5;
    const farmMilkMax = theoreticalFarmMilk * 1.5;
    const deliveryMilkMax = theoreticalDeliveryMilk * 1.5;
    
    console.log(`📊 1.5倍误差范围:`);
    console.log(`   GEM最大允许: ${gemMax.toFixed(3)}`);
    console.log(`   农场牛奶最大允许: ${farmMilkMax.toFixed(3)}`);
    console.log(`   出货线牛奶最大允许: ${deliveryMilkMax.toFixed(3)}`);
    
    // 分析前端请求是否在范围内
    if (requestData.gemRequest !== undefined) {
      const inRange = requestData.gemRequest <= gemMax;
      console.log(`💎 GEM请求分析:`);
      console.log(`   前端请求: ${requestData.gemRequest}`);
      console.log(`   是否在1.5倍范围内: ${inRange ? '✅ 是' : '❌ 否'}`);
    }
    
    if (requestData.milkOperations) {
      const produceInRange = (requestData.milkOperations.produce || 0) <= farmMilkMax;
      const consumeInRange = (requestData.milkOperations.consume || 0) <= deliveryMilkMax;
      
      console.log(`🥛 牛奶请求分析:`);
      console.log(`   前端请求生产: ${requestData.milkOperations.produce || 0} (范围内: ${produceInRange ? '✅' : '❌'})`);
      console.log(`   前端请求消耗: ${requestData.milkOperations.consume || 0} (范围内: ${consumeInRange ? '✅' : '❌'})`);
    }
    
    // 分析API返回的details值
    if (data.changes?.details) {
      const details = data.changes.details;
      console.log(`📋 API返回的details值:`);
      console.log(`   gem.increased: ${details.gem?.increased || 0}`);
      console.log(`   milk.increased: ${details.milk?.increased || 0}`);
      console.log(`   milk.decreased: ${details.milk?.decreased || 0}`);
      
      // 验证优先级逻辑
      console.log(`🎯 优先级逻辑验证:`);
      
      // 检查GEM
      if (requestData.gemRequest !== undefined) {
        const gemInRange = requestData.gemRequest <= gemMax;
        const expectedGemValue = gemInRange ? requestData.gemRequest : theoreticalGems;
        
        if (Math.abs(details.gem.increased - expectedGemValue) < 0.1) {
          console.log(`   ✅ GEM正确: ${gemInRange ? '使用前端请求值' : '使用系统计算值'} (${details.gem.increased})`);
        } else {
          console.log(`   ❌ GEM错误: 期望${expectedGemValue}，实际${details.gem.increased}`);
          console.log(`   💡 应该${gemInRange ? '使用前端请求值' : '使用系统计算值'}`);
        }
      } else {
        // 无前端请求，应该使用系统计算值
        if (Math.abs(details.gem.increased - theoreticalGems) < 0.1) {
          console.log(`   ✅ GEM正确: 无前端请求，使用系统计算值 (${details.gem.increased})`);
        } else {
          console.log(`   ❌ GEM错误: 无前端请求应使用系统值${theoreticalGems}，实际${details.gem.increased}`);
        }
      }
      
      // 检查牛奶
      if (requestData.milkOperations) {
        const produceInRange = (requestData.milkOperations.produce || 0) <= farmMilkMax;
        const consumeInRange = (requestData.milkOperations.consume || 0) <= deliveryMilkMax;
        const milkInRange = produceInRange && consumeInRange;
        
        const expectedMilkIncreased = milkInRange ? (requestData.milkOperations.produce || 0) : theoreticalFarmMilk;
        const expectedMilkDecreased = milkInRange ? (requestData.milkOperations.consume || 0) : theoreticalDeliveryMilk;
        
        if (Math.abs(details.milk.increased - expectedMilkIncreased) < 0.1) {
          console.log(`   ✅ 牛奶生产正确: ${milkInRange ? '使用前端请求值' : '使用系统计算值'} (${details.milk.increased})`);
        } else {
          console.log(`   ❌ 牛奶生产错误: 期望${expectedMilkIncreased}，实际${details.milk.increased}`);
        }
        
        if (Math.abs(details.milk.decreased - expectedMilkDecreased) < 0.1) {
          console.log(`   ✅ 牛奶消耗正确: ${milkInRange ? '使用前端请求值' : '使用系统计算值'} (${details.milk.decreased})`);
        } else {
          console.log(`   ❌ 牛奶消耗错误: 期望${expectedMilkDecreased}，实际${details.milk.decreased}`);
        }
      } else {
        // 无前端请求，应该使用系统计算值
        if (Math.abs(details.milk.increased - theoreticalFarmMilk) < 0.1 &&
            Math.abs(details.milk.decreased - theoreticalDeliveryMilk) < 0.1) {
          console.log(`   ✅ 牛奶正确: 无前端请求，使用系统计算值`);
        } else {
          console.log(`   ❌ 牛奶错误: 无前端请求应使用系统值`);
        }
      }
      
      // 检查是否使用了系统计算
      if (data.changes?.usedSystemCalculation) {
        console.log(`   📝 使用了系统计算: ${data.changes.systemCalculationReason}`);
      } else {
        console.log(`   📝 使用了前端请求值`);
      }
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testFrontendRequestPriority() {
  console.log('🧪 开始测试前端请求优先级逻辑...\n');
  console.log('🎯 测试目标:');
  console.log('1. 前端请求在1.5倍范围内 → details显示前端请求值');
  console.log('2. 前端请求超出1.5倍范围 → details显示系统计算值');
  console.log('3. 无前端请求 → details显示系统计算值');

  try {
    // 1. 测试合理的前端请求（应该在1.5倍范围内）
    console.log('\n📋 测试1: 合理的前端请求（应该使用前端值）');
    const request1 = {
      gemRequest: 3,
      milkOperations: {
        produce: 1,
        consume: 3
      }
    };
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeFrontendRequestPriority(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试超出范围的前端请求（应该使用系统计算值）
    console.log('\n📋 测试2: 超出范围的前端请求（应该使用系统值）');
    const request2 = {
      gemRequest: 100, // 明显超出范围
      milkOperations: {
        produce: 50, // 明显超出范围
        consume: 50  // 明显超出范围
      }
    };
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeFrontendRequestPriority(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试无前端请求（应该使用系统计算值）
    console.log('\n📋 测试3: 无前端请求（应该使用系统值）');
    const request3 = {};
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeFrontendRequestPriority(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 4. 测试边界情况（刚好在1.5倍边界）
    console.log('\n📋 测试4: 边界情况（刚好在1.5倍边界）');
    const request4 = {
      gemRequest: 7, // 可能刚好在边界
      milkOperations: {
        produce: 1,
        consume: 7
      }
    };
    
    try {
      const response4 = await api.post('/api/wallet/batch-update-resources', request4);
      console.log('✅ 请求成功');
      analyzeFrontendRequestPriority(response4, '测试4', request4);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 期望的逻辑：');
  console.log('✅ 前端请求 ≤ 系统计算值 × 1.5 → details显示前端请求值');
  console.log('✅ 前端请求 > 系统计算值 × 1.5 → details显示系统计算值');
  console.log('✅ 无前端请求 → details显示系统计算值');
  console.log('✅ 实际给用户的资源仍然基于验证结果');
}

// 运行测试
testFrontendRequestPriority();
