const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析简化逻辑的函数
function analyzeSimplifiedLogic(response, testName, requestData) {
  console.log(`\n📊 ${testName} - 简化逻辑验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    
    // 分析牛奶库存变化
    const initialPendingMilk = data.beforeUpdate?.pendingMilk || 0;
    const finalPendingMilk = data.afterUpdate?.pendingMilk || 0;
    const actualMilkChange = finalPendingMilk - initialPendingMilk;
    
    console.log(`🥛 牛奶库存分析:`);
    console.log(`   初始库存: ${initialPendingMilk} 牛奶`);
    console.log(`   最终库存: ${finalPendingMilk} 牛奶`);
    console.log(`   实际变化: ${actualMilkChange} 牛奶`);
    
    // 分析GEM变化
    const gemBefore = data.beforeUpdate?.gem || 0;
    const gemAfter = data.afterUpdate?.gem || 0;
    const actualGemChange = gemAfter - gemBefore;
    
    console.log(`💎 GEM分析:`);
    console.log(`   GEM变化: ${actualGemChange}`);
    
    // 分析details值
    if (data.changes?.details) {
      const details = data.changes.details;
      console.log(`📋 Details分析:`);
      console.log(`   gem.increased: ${details.gem?.increased || 0}`);
      console.log(`   milk.increased: ${details.milk?.increased || 0}`);
      console.log(`   milk.decreased: ${details.milk?.decreased || 0}`);
      
      // 验证合理性
      console.log(`🔍 合理性验证:`);
      
      // 检查牛奶消耗是否合理
      const maxPossibleConsumption = initialPendingMilk + (details.milk?.increased || 0);
      if ((details.milk?.decreased || 0) > maxPossibleConsumption) {
        console.log(`   ❌ 牛奶消耗不合理: 消耗${details.milk.decreased} > 可用${maxPossibleConsumption}`);
        console.log(`   💡 出货线不应该消耗不存在的牛奶`);
      } else {
        console.log(`   ✅ 牛奶消耗合理: 消耗${details.milk.decreased} ≤ 可用${maxPossibleConsumption}`);
      }
      
      // 检查最终库存计算
      const calculatedFinalMilk = initialPendingMilk + (details.milk?.increased || 0) - (details.milk?.decreased || 0);
      if (Math.abs(finalPendingMilk - calculatedFinalMilk) < 0.1) {
        console.log(`   ✅ 库存计算正确: ${finalPendingMilk} ≈ ${calculatedFinalMilk}`);
      } else {
        console.log(`   ❌ 库存计算错误: 实际${finalPendingMilk} ≠ 计算${calculatedFinalMilk}`);
      }
      
      // 检查GEM和牛奶消耗的关系
      const gemToMilkRatio = (details.milk?.decreased || 0) / (details.gem?.increased || 1);
      console.log(`   📊 GEM:牛奶比例: 1GEM = ${gemToMilkRatio.toFixed(1)}牛奶`);
      if (Math.abs(gemToMilkRatio - 1) < 0.1) {
        console.log(`   ✅ 比例正确: 1GEM = 1牛奶`);
      } else if (details.gem?.increased === 0 && details.milk?.decreased === 0) {
        console.log(`   ✅ 无生产无消耗，比例正常`);
      } else {
        console.log(`   ⚠️  比例异常，可能需要检查配置`);
      }
    }
    
    // 检查系统计算状态
    if (data.changes?.usedSystemCalculation) {
      console.log(`📝 系统计算: ${data.changes.systemCalculationReason}`);
    } else {
      console.log(`📝 使用前端请求值`);
    }
    
    // 特殊情况检查
    if (actualTimeDiff < 5) {
      console.log(`⚠️  时间过短（<5秒），应该没有资源变化`);
    } else if (actualTimeDiff > 120) {
      console.log(`⚠️  时间过长（>2分钟），用户离线，应该没有资源变化`);
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testSimplifiedLogic() {
  console.log('🧪 开始测试简化逻辑...\n');
  console.log('🎯 验证要点:');
  console.log('1. 每个用户都有出货线和农场，不需要检查存在性');
  console.log('2. 出货线不能消耗不存在的牛奶');
  console.log('3. details显示实际可执行的变化量');
  console.log('4. 库存计算正确');

  try {
    // 1. 测试基本请求
    console.log('\n📋 测试1: 基本请求');
    const request1 = {};
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeSimplifiedLogic(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试有参数请求
    console.log('\n📋 测试2: 有参数请求');
    const request2 = {
      gemRequest: 5,
      milkOperations: {
        produce: 2,
        consume: 3
      }
    };
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeSimplifiedLogic(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试超出范围请求
    console.log('\n📋 测试3: 超出范围请求');
    const request3 = {
      gemRequest: 100,
      milkOperations: {
        produce: 100,
        consume: 100
      }
    };
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeSimplifiedLogic(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 简化后的逻辑：');
  console.log('✅ 移除了不必要的出货线存在性检查');
  console.log('✅ 每个用户默认都有出货线和农场');
  console.log('✅ 协调计算确保牛奶消耗不超过可用量');
  console.log('✅ details显示实际执行的变化量');
}

// 运行测试
testSimplifiedLogic();
