# 批量资源更新 API 文档

## 概述

批量资源更新 API 是 Wolf Fun 游戏的核心资源管理接口，用于**带参数验证的智能资源更新**。该接口采用**1.5倍范围验证机制**，根据用户的 `lastActiveTime` 计算系统建议值，并验证用户请求是否合理，支持**5秒-2分钟**的在线用户高频请求，确保游戏经济平衡和防止异常请求。

## 接口信息

- **路径**: `/api/wallet/batch-update-resources`
- **方法**: POST
- **描述**: 基于时间差动态计算系统建议值，验证用户请求合理性，智能更新GEM和PendingMilk资源
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`
- **请求频率**: **5秒-1分钟**有效窗口，小于5秒或超过1分钟的请求将被阻止

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | JWT Bearer token |
| Content-Type | string | 是 | application/json |
| Accept-Language | string | 否 | 语言设置 (zh/en，默认: zh) |

### 请求体

| 参数名 | 类型 | 必填 | 描述 | 限制 |
| ------ | ---- | ---- | ---- | ---- |
| gemRequest | number | 否 | 前端请求的GEM增量 | 必须为非负数（GEM只会增加不会减少），系统验证是否在理论范围内 |
| milkOperations | object | 否 | 前端请求的牛奶操作 | 只支持对象形式（分别指定生产和消耗） |

**milkOperations 对象格式**：
| 字段名 | 类型 | 必填 | 描述 | 限制 |
| ------ | ---- | ---- | ---- | ---- |
| produce | number | 否 | 农场牛奶生产量 | 必须为非负数 |
| consume | number | 否 | 出货线牛奶消耗量 | 必须为非负数 |

**注意**:
- 可以不提供任何参数，系统将自动计算资源增量
- **前端逻辑验证机制**（根据流程图）：
  - 牛奶增量认证：前端牛奶增量应该在理论范围内（农场产量 - 出货线消耗）± 1.5倍误差范围
  - GEM增量认证：前端GEM增量应该基于出货线处理能力 × 时间 × 每block的GEM价格，最大1.5倍误差范围
- **重要**：GEM只会增加不会减少，增加GEM时不需要额外计算消耗PendingMilk
- **有效时间窗口：5秒-2分钟**
  - 小于5秒：防刷保护，获得0资源
  - 超过2分钟：判定为离线，获得0资源
  - 5秒-2分钟：正常在线用户，验证请求合理性

### 请求示例

**对象形式**（分别指定增加和减少）：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemRequest": 100.000,
    "milkOperations": {
      "produce": 50.000,
      "consume": 30.000
    }
  }'
```

**只增加牛奶**：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "milkOperations": {
      "produce": 50.000
    }
  }'
```

**只消耗牛奶**：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "milkOperations": {
      "consume": 30.000
    }
  }'
```

**无参数调用**（系统自动计算）：
```bash
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 响应格式

### 成功响应 (状态码: 200)

**合理请求响应**（前端增量验证通过）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167491.000,
      "pendingMilk": 4000.000,
      "lastActiveTime": "2025-06-21 11:27:02"
    },
    "afterUpdate": {
      "gem": 167591.000,
      "pendingMilk": 4100.000,
      "lastActiveTime": "2025-06-21 16:21:31"
    },
    "changes": {
      "productionRates": {
        "farmMilkPerCycle": 7.595,
        "deliveryBlockUnit": 655360,
        "deliveryBlockPrice": 655360,
        "timeElapsedSeconds": 6.393
      },
      "actual": {
        "gem": {
          "changed": 100.000
        },
        "milk": {
          "changed": 100.000
        }
      },
      "details": {
        "gem": {
          "increased": 13.8
        },
        "milk": {
          "increased": 63.0,
          "decreased": 48.0
        }
      }
    },
    "timestamp": "2025-06-21 16:21:31"
  },
  "message": "Resources updated successfully"
}
```

**不合理请求响应**（前端增量验证失败，使用系统调整值）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167491.000,
      "pendingMilk": 4000.000,
      "lastActiveTime": "2025-06-21 11:27:02"
    },
    "afterUpdate": {
      "gem": 167541.000,
      "pendingMilk": 4025.000,
      "lastActiveTime": "2025-06-21 16:21:31"
    },
    "changes": {
      "usedSystemCalculation": true,
      "systemCalculationReason": "GEM增长量 (10000.000) 超出最大允许值 (50.000)",
      "actual": {
        "gem": {
          "changed": 50.000
        },
        "milk": {
          "changed": 25.000
        }
      },
      "details": {
        "gem": {
          "increased": 50.000
        },
        "milk": {
          "increased": 25.000,
          "decreased": 0.000
        }
      }
    },
    "timestamp": "2025-06-21 16:21:31"
  },
  "message": "Resources updated successfully"
}
```

**高频请求响应**（5秒间隔，部分限制）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167591.000,
      "pendingMilk": 4100.000,
      "lastActiveTime": "2025-06-21 12:21:31"
    },
    "afterUpdate": {
      "gem": 167604.918,
      "pendingMilk": 4102.784,
      "lastActiveTime": "2025-06-21 16:21:44"
    },
    "changes": {
      "actual": {
        "gem": {
          "changed": 13.918
        },
        "milk": {
          "changed": 2.784
        }
      },
      "details": {
        "gem": {
          "increased": 13.918
        },
        "milk": {
          "increased": 2.784,
          "decreased": 0.000
        }
      }
    },
    "timestamp": "2025-06-21 16:21:44"
  },
  "message": "Resources updated successfully"
}
```

**防刷保护响应**（小于5秒请求）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 12:22:51"
    },
    "afterUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 16:22:51"
    },
    "changes": {
      "actual": {
        "gem": {
          "changed": 0.000
        },
        "milk": {
          "changed": 0.000
        }
      },
      "details": {
        "gem": {
          "increased": 0
        },
        "milk": {
          "increased": 0,
          "decreased": 0
        }
      }
    },
    "timestamp": "2025-06-21 16:22:51"
  },
  "message": "Resources updated successfully"
}
```

**离线用户响应**（超过1分钟请求）：
```json
{
  "ok": true,
  "data": {
    "beforeUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 11:20:00"
    },
    "afterUpdate": {
      "gem": 167676.000,
      "pendingMilk": 4116.900,
      "lastActiveTime": "2025-06-21 16:22:51"
    },
    "changes": {
      "actual": {
        "gem": {
          "changed": 0.000
        },
        "milk": {
          "changed": 0.000
        }
      },
      "details": {
        "gem": {
          "increased": 0
        },
        "milk": {
          "increased": 0,
          "decreased": 0
        }
      }
    },
    "timestamp": "2025-06-21 16:22:51"
  },
  "message": "Resources updated successfully"
}
```

## 响应字段说明

### 主要字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| ok | boolean | 请求是否成功 |
| data | object | 响应数据 |
| message | string | 操作结果消息 |

### data 字段详情

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| beforeUpdate | object | 更新前的资源状态 |
| afterUpdate | object | 更新后的资源状态 |
| changes | object | 本次更新的变化详情 |
| gameState | object | 当前游戏状态分析 |
| timestamp | string | 操作时间戳（格式：YYYY-MM-DD HH:mm:ss） |

### beforeUpdate/afterUpdate 字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| gem | number | GEM数量（3位小数精度） |
| pendingMilk | number | 待处理牛奶数量（3位小数精度） |
| lastActiveTime | string | 最后活跃时间（格式：YYYY-MM-DD HH:mm:ss） |

### changes 字段

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| usedSystemCalculation | boolean | 是否因请求不合理而使用了系统计算值 |
| systemCalculationReason | string | 使用系统计算值的原因说明 |
| productionRates | object | 生产速率调试信息 |
| details | object | 详细的增减信息 |

**productionRates 对象详情**：
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| farmMilkPerCycle | number | 农场每个生产周期的牛奶产量（基础配置） |
| deliveryBlockUnit | number | 出货线每个方块消耗的牛奶数量（基础配置） |
| deliveryBlockPrice | number | 出货线每个方块的GEM价格（基础配置） |
| timeElapsedSeconds | number | 实际经过的秒数 |

**details 对象详情**：
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| gem.increased | number | 实际GEM增加量 |
| milk.increased | number | 实际牛奶增加量 |
| milk.decreased | number | 实际牛奶减少量 |

## 错误响应

### 参数验证失败 (状态码: 400)

```json
{
  "ok": false,
  "message": "参数验证失败",
  "error": "至少需要提供一个资源更新参数（gemAmount 或 pendingMilkAmount）"
}
```

### 钱包不存在 (状态码: 404)

```json
{
  "ok": false,
  "message": "钱包不存在",
  "error": "用户钱包不存在"
}
```

### 权限不足 (状态码: 403)

```json
{
  "ok": false,
  "message": "未授权访问",
  "error": "权限不足"
}
```

### 服务器错误 (状态码: 500)

```json
{
  "ok": false,
  "message": "批量资源更新失败",
  "error": "内部服务器错误"
}
```

## 业务逻辑说明

### 1. 参数验证和范围检查

- 验证 gemAmount 和 pendingMilkAmount 为有效数值
- gemAmount 必须为非负数，pendingMilkAmount 可以为正数或负数
- 确保至少提供一个资源更新参数
- 验证用户JWT token有效性

### 2. 用户状态验证

- 检查用户钱包是否存在
- 验证用户权限
- 获取当前游戏状态

### 3. 基于时间的动态资源计算

**核心机制**：
- 根据用户的 `lastActiveTime` 精确计算从上次更新到现在应该获得的资源
- **5秒防刷保护**：距离上次更新小于5秒的请求将获得0资源
- **2分钟离线判定**：距离上次更新超过2分钟的请求将获得0资源
- **有效时间窗口**：只有5秒-2分钟内的请求才能获得基于时间计算的资源

**计算公式**：
- **GEM上限** = 出货线处理能力(单位/秒) × 经过时间(小时) × 3600
- **PendingMilk上限** = 农场总产能(牛奶/秒) × 经过时间(小时) × 3600

**1.5倍范围验证机制**：
- 系统计算基于时间的资源建议值
- 验证用户请求是否在1.5倍范围内
- 合理请求：按用户要求分配资源
- 不合理请求：使用系统计算值并标明原因

### 4. 游戏状态分析

- 计算农场总产量（所有已解锁区块的每秒产量之和）
- 计算出货线处理能力（方块单位 ÷ 出货速度）
- 分析产能平衡状态，识别瓶颈

### 5. 数据库操作

- 使用数据库事务确保数据一致性
- 更新用户钱包的 gem 字段
- 更新出货线的 pendingMilk 字段
- 创建详细的 WalletHistory 记录用于审计
- **统一更新 lastActiveTime 为当前时间**

### 6. 高精度计算

- 使用 BigNumber.js 确保数值计算精度
- 所有返回值保持3位小数精度
- 避免浮点数精度问题

## 使用场景

### 1. 合理资源请求（在1.5倍范围内）

```bash
# 请求合理数量的资源，系统验证通过后按请求分配
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemAmount": 50,
    "pendingMilkAmount": 25
  }'
```

### 2. 减少牛奶资源

```bash
# 减少待处理牛奶，支持负数操作
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pendingMilkAmount": -30
  }'
```

### 3. 不合理请求处理

```bash
# 请求过大数量，系统自动使用计算值并说明原因
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "gemAmount": 10000,
    "pendingMilkAmount": 5000
  }'
```

## 时间机制详解

### 1. 时间窗口机制（5秒-2分钟）

**有效时间窗口**：
- 系统会检查距离上次更新的时间间隔
- **小于5秒**：防刷保护，资源上限设为0
- **5秒-2分钟**：正常在线用户，基于时间计算资源
- **超过2分钟**：离线用户，资源上限设为0
- 请求仍然成功，但只有在有效窗口内才能获得资源

**时间计算**：
```
时间差(小时) = (当前时间 - lastActiveTime) / 3600000毫秒
如果时间差 < 5/3600 小时，则上限 = 0 (防刷保护)
如果时间差 > 2/60 小时，则上限 = 0 (离线判定)
如果 5/3600 <= 时间差 <= 2/60，则正常计算资源上限
```

### 2. 动态资源计算

**GEM计算示例**：
```
出货线处理能力 = 1.0 单位/秒
经过时间 = 0.5 小时
GEM上限 = 1.0 × 0.5 × 3600 = 1800 GEM
```

**PendingMilk计算示例**：
```
农场总产能 = 0.2 牛奶/秒
经过时间 = 0.5 小时
PendingMilk上限 = 0.2 × 0.5 × 3600 = 360 牛奶
```

### 3. 实际应用场景

| 时间间隔 | 场景描述 | 资源获得 | 适用情况 | 状态 |
| -------- | -------- | -------- | -------- | ---- |
| < 5秒 | 快速连续请求 | 0资源 | 防刷保护 | 在线 |
| 5秒-2分钟 | 实时游戏操作 | 基于时间计算 | 正常游戏 | 在线 |
| > 2分钟 | 离线用户 | 0资源 | 离线判定 | 离线 |

**注意**：只有在5秒-1分钟的时间窗口内，用户才能获得基于时间计算的资源。

## 注意事项

### 1. 数据精度

- 所有数值计算使用 BigNumber.js 确保精度
- 输入和输出值均保持3位小数精度
- 避免 JavaScript 原生数值计算的精度问题

### 2. 基于时间的动态资源机制

**核心特性**：
- **5秒防刷保护**：小于5秒的请求将获得0资源，有效防止刷取
- **2分钟离线判定**：超过2分钟的请求将获得0资源，判定为离线用户
- **有效时间窗口**：只有5秒-2分钟内的请求才能获得资源
- **动态时间计算**：根据 `lastActiveTime` 精确计算应得资源
- **实时游戏支持**：支持在线用户的高频请求，适合实时游戏体验

**计算公式**：
- **GEM上限** = 出货线处理能力(单位/秒) × 经过时间(小时) × 3600
- **PendingMilk上限** = 农场总产能(牛奶/秒) × 经过时间(小时) × 3600

**实际示例**：
- 经过30秒（在有效窗口内），出货线处理能力1单位/秒 → GEM上限 = 1 × (30/3600) × 3600 = 30
- 经过10秒（在有效窗口内），农场产能0.2牛奶/秒 → PendingMilk上限 = 0.2 × (10/3600) × 3600 = 2
- 经过3秒 → 所有资源上限 = 0（防刷保护）
- 经过2分钟 → 所有资源上限 = 0（离线判定）

**游戏经济平衡**：
- 防止无限制刷取资源
- 确保资源增长与实际游戏进度和时间相匹配
- 基于真实的游戏产能计算，维护游戏经济平衡

### 3. 用户活跃状态

- 每次成功调用都会更新用户的 lastActiveTime
- 确保用户被正确标记为在线状态
- 影响离线奖励的计算

### 4. 审计追踪

- 所有资源变更都会创建 WalletHistory 记录
- 支持完整的审计追踪
- 记录操作时间、数量、类型等详细信息

### 5. 错误处理

- 提供详细的错误信息和错误码
- 支持多语言错误消息
- 所有错误都会记录到系统日志

### 6. 性能考虑

- 使用数据库事务确保数据一致性
- 单次请求处理多个资源更新，减少数据库访问
- 智能的游戏状态分析，提供有价值的反馈信息
- 支持高频请求（5秒间隔），适合实时游戏需求

## API测试示例

### 1. 测试时间窗口机制

```bash
# 第一次请求（正常，在有效窗口内）
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 立即第二次请求（防刷保护，小于5秒）
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 等待6秒后第三次请求（正常，在有效窗口内）
sleep 6
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 等待2分钟后第四次请求（离线判定，超过1分钟）
sleep 120
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. 测试离线用户

```bash
# 模拟离线用户的资源请求（超过1分钟）
curl -X POST http://localhost:3456/api/wallet/batch-update-resources \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. 预期响应分析

**5秒内请求（防刷保护）**：
- `details.gem.increased`: 0
- `details.milk.increased`: 0
- `details.milk.decreased`: 0

**5秒-2分钟请求（正常在线用户）**：
- 合理请求：按用户请求数量分配
- 不合理请求：使用系统计算值，并标明 `usedSystemCalculation: true`

**超过2分钟请求（离线用户）**：
- `details.gem.increased`: 0
- `details.milk.increased`: 0
- `details.milk.decreased`: 0

**1.5倍范围验证**：
- `gemAmount` 范围：`[0, 系统上限 × 1.5]`
- `pendingMilkAmount` 范围：`[系统上限 × -1.5, 系统上限 × 1.5]`
