const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析协调生产的函数
function analyzeCoordinatedProduction(response, testName, requestData) {
  console.log(`\n📊 ${testName} - 协调生产逻辑验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    
    // 分析初始状态
    const initialPendingMilk = data.beforeUpdate?.pendingMilk || 0;
    const finalPendingMilk = data.afterUpdate?.pendingMilk || 0;
    const actualMilkChange = finalPendingMilk - initialPendingMilk;
    
    console.log(`🥛 牛奶库存分析:`);
    console.log(`   初始库存: ${initialPendingMilk} 牛奶`);
    console.log(`   最终库存: ${finalPendingMilk} 牛奶`);
    console.log(`   实际变化: ${actualMilkChange} 牛奶`);
    
    // 基于游戏配置进行协调计算验证
    console.log(`🔧 游戏配置分析:`);
    console.log(`   农场: 5秒产1牛奶`);
    console.log(`   出货线: 5秒消耗5牛奶，产5GEM`);
    
    // 模拟协调生产过程
    const farmCycles = Math.floor(actualTimeDiff / 5); // 农场周期数
    const deliveryCycles = Math.floor(actualTimeDiff / 5); // 出货线周期数
    
    console.log(`📈 理论独立计算:`);
    console.log(`   农场周期数: ${farmCycles}`);
    console.log(`   出货线周期数: ${deliveryCycles}`);
    console.log(`   农场理论产量: ${farmCycles * 1} 牛奶`);
    console.log(`   出货线理论消耗: ${deliveryCycles * 5} 牛奶`);
    console.log(`   出货线理论GEM产量: ${deliveryCycles * 5} GEM`);
    
    // 协调生产计算
    let simulatedPendingMilk = initialPendingMilk;
    let simulatedFarmProduced = 0;
    let simulatedDeliveryConsumed = 0;
    let simulatedGemProduced = 0;
    
    // 按时间顺序模拟
    for (let time = 5; time <= actualTimeDiff; time += 5) {
      // 农场生产（每5秒产1牛奶）
      simulatedPendingMilk += 1;
      simulatedFarmProduced += 1;
      
      // 出货线尝试处理（每5秒消耗5牛奶，产5GEM）
      if (simulatedPendingMilk >= 5) {
        simulatedPendingMilk -= 5;
        simulatedDeliveryConsumed += 5;
        simulatedGemProduced += 5;
      }
      // 如果牛奶不足5，出货线等待，不处理
    }
    
    console.log(`🎯 协调生产模拟:`);
    console.log(`   模拟农场产量: ${simulatedFarmProduced} 牛奶`);
    console.log(`   模拟出货线消耗: ${simulatedDeliveryConsumed} 牛奶`);
    console.log(`   模拟GEM产量: ${simulatedGemProduced} GEM`);
    console.log(`   模拟最终库存: ${simulatedPendingMilk} 牛奶`);
    
    // 分析API返回的details值
    if (data.changes?.details) {
      const details = data.changes.details;
      console.log(`📋 API返回的details值:`);
      console.log(`   gem.increased: ${details.gem?.increased || 0}`);
      console.log(`   milk.increased: ${details.milk?.increased || 0}`);
      console.log(`   milk.decreased: ${details.milk?.decreased || 0}`);
      
      // 验证协调逻辑
      console.log(`🔍 协调逻辑验证:`);
      
      // 检查是否使用了协调计算
      if (Math.abs(details.gem.increased - simulatedGemProduced) < 0.1) {
        console.log(`   ✅ GEM使用协调计算 (${details.gem.increased} ≈ ${simulatedGemProduced})`);
      } else {
        console.log(`   ❌ GEM未使用协调计算 (${details.gem.increased} ≠ ${simulatedGemProduced})`);
        console.log(`   💡 可能仍在使用独立计算，忽略了牛奶不足的情况`);
      }
      
      if (Math.abs(details.milk.increased - simulatedFarmProduced) < 0.1) {
        console.log(`   ✅ 牛奶生产使用协调计算 (${details.milk.increased} ≈ ${simulatedFarmProduced})`);
      } else {
        console.log(`   ❌ 牛奶生产未使用协调计算 (${details.milk.increased} ≠ ${simulatedFarmProduced})`);
      }
      
      if (Math.abs(details.milk.decreased - simulatedDeliveryConsumed) < 0.1) {
        console.log(`   ✅ 牛奶消耗使用协调计算 (${details.milk.decreased} ≈ ${simulatedDeliveryConsumed})`);
      } else {
        console.log(`   ❌ 牛奶消耗未使用协调计算 (${details.milk.decreased} ≠ ${simulatedDeliveryConsumed})`);
        console.log(`   💡 应该考虑牛奶库存限制，不能凭空消耗`);
      }
      
      // 验证最终库存
      if (Math.abs(finalPendingMilk - simulatedPendingMilk) < 0.1) {
        console.log(`   ✅ 最终库存符合协调计算 (${finalPendingMilk} ≈ ${simulatedPendingMilk})`);
      } else {
        console.log(`   ❌ 最终库存不符合协调计算 (${finalPendingMilk} ≠ ${simulatedPendingMilk})`);
      }
    }
    
    // 分析实际资源变化
    const gemBefore = data.beforeUpdate?.gem || 0;
    const gemAfter = data.afterUpdate?.gem || 0;
    const actualGemChange = gemAfter - gemBefore;
    
    console.log(`💰 实际资源变化:`);
    console.log(`   GEM变化: ${actualGemChange}`);
    console.log(`   牛奶变化: ${actualMilkChange}`);
    
    // 特殊情况分析
    if (initialPendingMilk === 0) {
      console.log(`\n🎯 特殊情况分析 - 初始库存为0:`);
      console.log(`   在这种情况下，出货线必须等待农场生产`);
      console.log(`   出货线不能"凭空"消耗不存在的牛奶`);
      
      if (details?.milk?.decreased > details?.milk?.increased) {
        console.log(`   ❌ 错误：消耗量(${details.milk.decreased}) > 生产量(${details.milk.increased})`);
        console.log(`   💡 这在初始库存为0时是不可能的`);
      } else {
        console.log(`   ✅ 正确：消耗量不超过可用量`);
      }
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testCoordinatedProduction() {
  console.log('🧪 开始测试协调生产逻辑...\n');
  console.log('🎯 测试目标:');
  console.log('1. 农场和出货线协调工作，不独立计算');
  console.log('2. 出货线不能消耗不存在的牛奶');
  console.log('3. 当牛奶不足时，出货线等待农场生产');
  console.log('4. details显示实际可执行的变化量');

  try {
    // 1. 测试短时间（一个周期）
    console.log('\n📋 测试1: 短时间请求（约6秒，1个周期）');
    const request1 = {};
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeCoordinatedProduction(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待11秒（约2个周期）
    console.log('\n⏳ 等待11秒（约2个周期）...');
    await new Promise(resolve => setTimeout(resolve, 11000));

    // 2. 测试多个周期
    console.log('\n📋 测试2: 多个周期（约11秒，2个周期）');
    const request2 = {};
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeCoordinatedProduction(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待26秒（约5个周期）
    console.log('\n⏳ 等待26秒（约5个周期）...');
    await new Promise(resolve => setTimeout(resolve, 26000));

    // 3. 测试更长时间
    console.log('\n📋 测试3: 更长时间（约26秒，5个周期）');
    const request3 = {};
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeCoordinatedProduction(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 期望的协调逻辑：');
  console.log('✅ 农场生产 → 牛奶库存增加');
  console.log('✅ 出货线检查库存 → 足够则处理，不足则等待');
  console.log('✅ details显示实际执行的生产和消费量');
  console.log('✅ 不会出现"凭空消耗"牛奶的情况');
}

// 运行测试
testCoordinatedProduction();
