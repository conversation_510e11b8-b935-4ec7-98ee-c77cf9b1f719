const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析牛奶计算的函数
function analyzeMilkCalculation(response, testName, requestData) {
  console.log(`\n📊 ${testName} - 牛奶计算修复验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    console.log(`   API报告时间: ${data.changes?.productionRates?.timeElapsedSeconds || 'N/A'} 秒`);
    
    // 分析生产速率
    if (data.changes?.productionRates) {
      const rates = data.changes.productionRates;
      console.log(`🏭 生产速率:`);
      console.log(`   农场产量: ${rates.milkPerSecond} 牛奶/秒`);
      console.log(`   出货线产量: ${rates.gemPerSecond} GEM/秒`);
      
      // 计算理论值
      const timeSeconds = rates.timeElapsedSeconds;
      const expectedMilkProduction = rates.milkPerSecond * timeSeconds;
      const expectedMilkConsumption = rates.gemPerSecond * timeSeconds; // 假设1 GEM = 1 牛奶消耗
      
      console.log(`📈 理论计算:`);
      console.log(`   理论牛奶生产: ${expectedMilkProduction.toFixed(3)}`);
      console.log(`   理论牛奶消耗: ${expectedMilkConsumption.toFixed(3)}`);
      console.log(`   理论净变化: ${(expectedMilkProduction - expectedMilkConsumption).toFixed(3)}`);
      
      // 分析实际返回值
      if (data.changes?.details?.milk) {
        const milk = data.changes.details.milk;
        console.log(`📋 API返回值:`);
        console.log(`   牛奶增加: ${milk.increased}`);
        console.log(`   牛奶减少: ${milk.decreased}`);
        console.log(`   净变化: ${milk.increased - milk.decreased}`);
        
        // 验证是否修复
        console.log(`🔍 修复验证:`);
        if (Math.abs(milk.increased - expectedMilkProduction) < 0.1) {
          console.log(`   ✅ 牛奶生产计算正确 (${milk.increased} ≈ ${expectedMilkProduction.toFixed(3)})`);
        } else {
          console.log(`   ❌ 牛奶生产计算错误 (${milk.increased} ≠ ${expectedMilkProduction.toFixed(3)})`);
        }
        
        if (Math.abs(milk.decreased - expectedMilkConsumption) < 0.1) {
          console.log(`   ✅ 牛奶消耗计算正确 (${milk.decreased} ≈ ${expectedMilkConsumption.toFixed(3)})`);
        } else {
          console.log(`   ❌ 牛奶消耗计算错误 (${milk.decreased} ≠ ${expectedMilkConsumption.toFixed(3)})`);
        }
        
        // 检查是否还在显示前端请求值
        if (requestData.tempMilk) {
          const frontendAdd = requestData.tempMilk.add || 0;
          const frontendReduce = requestData.tempMilk.reduce || 0;
          
          if (milk.increased === frontendAdd && milk.decreased === frontendReduce) {
            console.log(`   ❌ 仍在显示前端请求值 (${frontendAdd}/${frontendReduce})，未使用真实计算`);
          } else {
            console.log(`   ✅ 已使用真实计算值，不再显示前端请求值`);
          }
        }
      }
    }
    
    // 分析GEM变化
    const gemBefore = data.beforeUpdate?.gem || 0;
    const gemAfter = data.afterUpdate?.gem || 0;
    const actualGemChange = gemAfter - gemBefore;
    
    console.log(`💎 GEM分析:`);
    console.log(`   请求增量: ${requestData.tempGem || 0}`);
    console.log(`   实际变化: ${actualGemChange}`);
    console.log(`   API显示增加: ${data.changes?.details?.gem?.increased || 0}`);
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testMilkCalculationFix() {
  console.log('🧪 开始测试牛奶计算修复...\n');

  try {
    // 1. 测试你提供的参数（净值为0的情况）
    console.log('📋 测试1: 净值为0的牛奶请求');
    const request1 = {
      tempGem: 100,
      tempMilk: {
        add: 100,
        reduce: 100
      }
    };
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeMilkCalculation(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试有净值的牛奶请求
    console.log('\n📋 测试2: 有净值的牛奶请求');
    const request2 = {
      tempGem: 50,
      tempMilk: {
        add: 80,
        reduce: 60
      }
    };
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeMilkCalculation(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试只有GEM的请求
    console.log('\n📋 测试3: 只有GEM的请求');
    const request3 = {
      tempGem: 30
    };
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeMilkCalculation(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 期望的修复效果：');
  console.log('✅ details.milk.increased 应该显示基于农场生产速率的真实值');
  console.log('✅ details.milk.decreased 应该显示基于出货线消耗速率的真实值');
  console.log('✅ 不再显示前端请求的原始值');
  console.log('✅ 即使净值为0也要显示真实的生产和消耗量');
}

// 运行测试
testMilkCalculationFix();
