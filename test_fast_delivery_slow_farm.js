// 测试出货线快、农场慢的情况
// 验证出货线等待农场生产的逻辑

console.log('🧪 测试出货线快、农场慢的情况');

// 模拟出货线很快、农场很慢的场景
const slowFarmPlots = [
  { productionSpeed: 20, milkProduction: 15 }, // 每20秒产15牛奶
];

const fastDeliveryLine = {
  deliverySpeed: 3,    // 每3秒处理一次（很快）
  blockUnit: 10,       // 每次消耗10牛奶
  blockPrice: 2        // 每次产出2GEM
};

// 模拟协调计算函数
function calculateTimeBasedCoordination(farmPlots, deliveryLine, timeElapsedSeconds, initialPendingMilk) {
  let currentPendingMilk = initialPendingMilk;
  let totalFarmProduced = 0;
  let totalDeliveryConsumed = 0;
  let totalGemProduced = 0;

  // 创建事件队列
  const events = [];

  // 添加农场生产事件
  farmPlots.forEach((plot, index) => {
    let nextProductionTime = plot.productionSpeed;
    while (nextProductionTime <= timeElapsedSeconds) {
      events.push({
        time: nextProductionTime,
        type: 'farm',
        plotIndex: index,
        amount: plot.milkProduction
      });
      nextProductionTime += plot.productionSpeed;
    }
  });

  // 添加出货线处理事件
  let nextDeliveryTime = deliveryLine.deliverySpeed;
  while (nextDeliveryTime <= timeElapsedSeconds) {
    events.push({
      time: nextDeliveryTime,
      type: 'delivery',
      amount: deliveryLine.blockUnit
    });
    nextDeliveryTime += deliveryLine.deliverySpeed;
  }

  // 按时间排序事件
  events.sort((a, b) => a.time - b.time);

  console.log('\n📅 事件时间线：');
  events.forEach(event => {
    console.log(`时间 ${event.time}s: ${event.type === 'farm' ? '农场生产' : '出货线处理'} ${event.amount} ${event.type === 'farm' ? '牛奶' : '牛奶需求'}`);
  });

  console.log('\n🔄 处理过程：');
  console.log(`初始库存: ${currentPendingMilk} 牛奶`);

  let waitingCount = 0; // 统计出货线等待次数

  // 处理事件队列
  for (const event of events) {
    if (event.type === 'farm') {
      // 农场生产事件
      currentPendingMilk += event.amount;
      totalFarmProduced += event.amount;
      console.log(`时间 ${event.time}s: 农场${event.plotIndex + 1}生产 ${event.amount} 牛奶，库存: ${currentPendingMilk}`);
    } else if (event.type === 'delivery') {
      // 出货线处理事件
      const requiredMilk = event.amount;
      
      if (currentPendingMilk >= requiredMilk) {
        // 有足够牛奶，可以处理
        currentPendingMilk -= requiredMilk;
        totalDeliveryConsumed += requiredMilk;
        totalGemProduced += deliveryLine.blockPrice;
        console.log(`时间 ${event.time}s: 出货线消耗 ${requiredMilk} 牛奶，产出 ${deliveryLine.blockPrice} GEM，库存: ${currentPendingMilk}`);
      } else {
        // 牛奶不够，出货线等待
        waitingCount++;
        console.log(`时间 ${event.time}s: 出货线等待（需要 ${requiredMilk} 牛奶，但只有 ${currentPendingMilk}），库存: ${currentPendingMilk}`);
      }
    }
  }

  return {
    farmProduced: totalFarmProduced,
    deliveryConsumed: totalDeliveryConsumed,
    gemProduced: totalGemProduced,
    finalPendingMilk: currentPendingMilk,
    waitingCount: waitingCount
  };
}

// 测试场景1：出货线很快，农场很慢，无初始库存
console.log('\n📋 测试场景1：出货线很快，农场很慢，无初始库存');
const result1 = calculateTimeBasedCoordination(slowFarmPlots, fastDeliveryLine, 60, 0);
console.log('\n📊 结果：');
console.log(`农场总产量: ${result1.farmProduced} 牛奶`);
console.log(`出货线消耗: ${result1.deliveryConsumed} 牛奶`);
console.log(`GEM产出: ${result1.gemProduced} GEM`);
console.log(`最终库存: ${result1.finalPendingMilk} 牛奶`);
console.log(`出货线等待次数: ${result1.waitingCount} 次`);

// 测试场景2：有一些初始库存
console.log('\n📋 测试场景2：有初始库存的情况');
const result2 = calculateTimeBasedCoordination(slowFarmPlots, fastDeliveryLine, 60, 25);
console.log('\n📊 结果：');
console.log(`农场总产量: ${result2.farmProduced} 牛奶`);
console.log(`出货线消耗: ${result2.deliveryConsumed} 牛奶`);
console.log(`GEM产出: ${result2.gemProduced} GEM`);
console.log(`最终库存: ${result2.finalPendingMilk} 牛奶`);
console.log(`出货线等待次数: ${result2.waitingCount} 次`);

// 对比传统计算方法
console.log('\n📋 对比传统计算方法：');
function traditionalCalculation(farmPlots, deliveryLine, timeElapsedSeconds, initialPendingMilk) {
  // 传统方法：独立计算农场和出货线
  let totalFarmProduced = 0;
  farmPlots.forEach(plot => {
    const cycles = Math.floor(timeElapsedSeconds / plot.productionSpeed);
    totalFarmProduced += cycles * plot.milkProduction;
  });

  const totalAvailableMilk = initialPendingMilk + totalFarmProduced;
  const deliveryCycles = Math.floor(timeElapsedSeconds / deliveryLine.deliverySpeed);
  const theoreticalDeliveryConsumption = deliveryCycles * deliveryLine.blockUnit;
  const actualDeliveryConsumption = Math.min(theoreticalDeliveryConsumption, totalAvailableMilk);
  const gemProduced = (actualDeliveryConsumption / deliveryLine.blockUnit) * deliveryLine.blockPrice;

  return {
    farmProduced: totalFarmProduced,
    deliveryConsumed: actualDeliveryConsumption,
    gemProduced: gemProduced,
    finalPendingMilk: totalAvailableMilk - actualDeliveryConsumption,
    theoreticalDeliveryCycles: deliveryCycles,
    actualDeliveryCycles: Math.floor(actualDeliveryConsumption / deliveryLine.blockUnit)
  };
}

const traditionalResult = traditionalCalculation(slowFarmPlots, fastDeliveryLine, 60, 0);
console.log('传统方法结果（无初始库存）：');
console.log(`农场总产量: ${traditionalResult.farmProduced} 牛奶`);
console.log(`出货线消耗: ${traditionalResult.deliveryConsumed} 牛奶`);
console.log(`GEM产出: ${traditionalResult.gemProduced} GEM`);
console.log(`最终库存: ${traditionalResult.finalPendingMilk} 牛奶`);
console.log(`理论出货周期: ${traditionalResult.theoreticalDeliveryCycles} 次`);
console.log(`实际出货周期: ${traditionalResult.actualDeliveryCycles} 次`);

console.log('\n🎯 关键观察：');
console.log('1. 出货线频繁尝试处理（每3秒一次），但经常因为牛奶不足而等待');
console.log('2. 农场生产慢（每20秒一次），成为整个系统的瓶颈');
console.log('3. 新方法准确显示了出货线的等待情况');
console.log('4. 传统方法可能高估了出货线的实际处理能力');

console.log('\n📈 效率分析：');
const farmProductionRate = slowFarmPlots[0].milkProduction / slowFarmPlots[0].productionSpeed;
const deliveryConsumptionRate = fastDeliveryLine.blockUnit / fastDeliveryLine.deliverySpeed;
console.log(`农场生产速率: ${farmProductionRate.toFixed(3)} 牛奶/秒`);
console.log(`出货线消耗速率: ${deliveryConsumptionRate.toFixed(3)} 牛奶/秒`);
console.log(`瓶颈: ${farmProductionRate < deliveryConsumptionRate ? '农场生产' : '出货线处理'}`);

console.log('\n✅ 出货线快、农场慢的情况测试完成！');
