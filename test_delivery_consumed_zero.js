// 测试 deliveryMilkConsumed 为 0 的问题
// 分析可能的原因和解决方案

console.log('🧪 测试 deliveryMilkConsumed 为 0 的问题');

// 模拟用户的实际数据
const userData = {
  beforeUpdate: {
    gem: 999999984369008600,
    pendingMilk: 1454.885,
    lastActiveTime: "2025-06-25 23:39:39"
  },
  afterUpdate: {
    gem: 999999984370319400,
    pendingMilk: 1470.075,
    lastActiveTime: "2025-06-25 23:39:48"
  },
  timeElapsedSeconds: 9.574,
  farmMilkProduced: 7.595,
  deliveryMilkConsumed: 0, // 问题：为什么是0？
  gemProduced: 1310720
};

console.log('\n📊 用户数据分析：');
console.log(`时间间隔: ${userData.timeElapsedSeconds} 秒`);
console.log(`农场生产: ${userData.farmMilkProduced} 牛奶`);
console.log(`出货线消耗: ${userData.deliveryMilkConsumed} 牛奶 ❌`);
console.log(`GEM产出: ${userData.gemProduced} GEM`);

// 计算实际的资源变化
const actualGemChange = userData.afterUpdate.gem - userData.beforeUpdate.gem;
const actualMilkChange = userData.afterUpdate.pendingMilk - userData.beforeUpdate.pendingMilk;

console.log('\n🔍 实际资源变化：');
console.log(`实际GEM变化: ${actualGemChange}`);
console.log(`实际牛奶变化: ${actualMilkChange.toFixed(3)}`);

// 分析问题原因
console.log('\n🎯 问题分析：');

console.log('\n1. 条件判断问题：');
console.log(`   finalTempGem === 0: ${actualGemChange === 0 ? 'true' : 'false'}`);
console.log(`   finalTempMilk === 0: ${actualMilkChange === 0 ? 'false (实际有变化)' : 'true'}`);
console.log('   → 原来的条件 (finalTempGem === 0 && finalTempMilk === 0) 不成立');

console.log('\n2. 数据库变化检查：');
console.log(`   actualGemChange === 0: ${actualGemChange === 0 ? 'false (有GEM增加)' : 'true'}`);
console.log(`   actualMilkChange === 0: ${actualMilkChange === 0 ? 'true' : 'false (有牛奶增加)'}`);
console.log('   → 新的条件 (actualGemChange === 0 && actualMilkChange === 0) 也不成立');

console.log('\n3. 可能的原因：');
console.log('   a) 协调计算正确执行，但出货线确实没有消耗牛奶');
console.log('   b) 出货线速度太慢，在这个时间段内没有完成任何处理周期');
console.log('   c) 牛奶库存不足，出货线一直在等待');

// 模拟出货线计算
console.log('\n🏭 出货线处理分析：');

// 假设出货线配置（基于之前的数据）
const deliveryLineConfig = {
  deliverySpeed: 5,    // 5秒/次
  blockUnit: 5,        // 5牛奶/次
  blockPrice: 5        // 5GEM/次
};

console.log(`出货线配置:`);
console.log(`  处理速度: ${deliveryLineConfig.deliverySpeed} 秒/次`);
console.log(`  牛奶消耗: ${deliveryLineConfig.blockUnit} 牛奶/次`);
console.log(`  GEM产出: ${deliveryLineConfig.blockPrice} GEM/次`);

// 计算理论处理能力
const theoreticalCycles = Math.floor(userData.timeElapsedSeconds / deliveryLineConfig.deliverySpeed);
const theoreticalMilkConsumption = theoreticalCycles * deliveryLineConfig.blockUnit;
const theoreticalGemProduction = theoreticalCycles * deliveryLineConfig.blockPrice;

console.log(`\n理论计算（独立计算）:`);
console.log(`  可完成周期: ${theoreticalCycles} 次`);
console.log(`  理论牛奶消耗: ${theoreticalMilkConsumption} 牛奶`);
console.log(`  理论GEM产出: ${theoreticalGemProduction} GEM`);

// 分析实际情况
console.log(`\n实际情况分析:`);
console.log(`  实际GEM产出: ${userData.gemProduced} GEM`);
console.log(`  GEM产出倍数: ${(userData.gemProduced / theoreticalGemProduction).toFixed(0)}x`);

// 如果GEM产出是理论值的很多倍，说明可能有其他因素
if (userData.gemProduced > theoreticalGemProduction * 10) {
  console.log('  → GEM产出远超理论值，可能有加速效果或其他机制');
}

// 基于时间的协调计算模拟
console.log('\n⏰ 基于时间的协调计算模拟：');

function simulateTimeBasedCoordination(timeElapsed, initialMilk, farmProduction, deliveryConfig) {
  let currentMilk = initialMilk;
  let totalFarmProduced = 0;
  let totalDeliveryConsumed = 0;
  let totalGemProduced = 0;

  // 模拟农场生产（假设每5秒生产一次）
  const farmSpeed = 5; // 假设农场也是5秒/次
  const farmCycles = Math.floor(timeElapsed / farmSpeed);
  const farmProductionPerCycle = farmProduction / farmCycles || 0;

  console.log(`  农场配置: ${farmSpeed}秒/次, ${farmProductionPerCycle.toFixed(3)}牛奶/次`);

  // 创建事件时间线
  const events = [];
  
  // 农场生产事件
  for (let i = 1; i <= farmCycles; i++) {
    events.push({
      time: i * farmSpeed,
      type: 'farm',
      amount: farmProductionPerCycle
    });
  }

  // 出货线处理事件
  const deliveryCycles = Math.floor(timeElapsed / deliveryConfig.deliverySpeed);
  for (let i = 1; i <= deliveryCycles; i++) {
    events.push({
      time: i * deliveryConfig.deliverySpeed,
      type: 'delivery',
      amount: deliveryConfig.blockUnit
    });
  }

  // 排序事件
  events.sort((a, b) => a.time - b.time);

  console.log(`  事件数量: ${events.length} (农场: ${farmCycles}, 出货线: ${deliveryCycles})`);

  // 处理事件
  for (const event of events) {
    if (event.type === 'farm') {
      currentMilk += event.amount;
      totalFarmProduced += event.amount;
    } else if (event.type === 'delivery') {
      if (currentMilk >= event.amount) {
        currentMilk -= event.amount;
        totalDeliveryConsumed += event.amount;
        totalGemProduced += deliveryConfig.blockPrice;
      }
    }
  }

  return {
    farmProduced: totalFarmProduced,
    deliveryConsumed: totalDeliveryConsumed,
    gemProduced: totalGemProduced,
    finalMilk: currentMilk
  };
}

const simulationResult = simulateTimeBasedCoordination(
  userData.timeElapsedSeconds,
  userData.beforeUpdate.pendingMilk,
  userData.farmMilkProduced,
  deliveryLineConfig
);

console.log(`\n协调计算结果:`);
console.log(`  农场生产: ${simulationResult.farmProduced.toFixed(3)} 牛奶`);
console.log(`  出货线消耗: ${simulationResult.deliveryConsumed.toFixed(3)} 牛奶`);
console.log(`  GEM产出: ${simulationResult.gemProduced} GEM`);
console.log(`  最终库存: ${simulationResult.finalMilk.toFixed(3)} 牛奶`);

console.log('\n🔧 可能的解决方案：');
console.log('1. 检查出货线配置是否正确');
console.log('2. 确认协调计算逻辑是否被正确调用');
console.log('3. 验证时间依赖计算是否考虑了所有因素');
console.log('4. 检查是否有特殊的游戏机制（如加速、VIP等）影响计算');

console.log('\n✅ deliveryMilkConsumed 为 0 的问题分析完成！');
