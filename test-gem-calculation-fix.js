const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析GEM计算的函数
function analyzeGemCalculation(response, testName, requestData) {
  console.log(`\n📊 ${testName} - GEM计算修复验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    console.log(`   API报告时间: ${data.changes?.productionRates?.timeElapsedSeconds || 'N/A'} 秒`);
    
    // 分析生产速率
    if (data.changes?.productionRates) {
      const rates = data.changes.productionRates;
      console.log(`🏭 API报告的生产速率:`);
      console.log(`   农场产量: ${rates.milkPerSecond} 牛奶/秒`);
      console.log(`   出货线产量: ${rates.gemPerSecond} GEM/秒`);
      
      // 基于你提供的出货线配置验证
      console.log(`🔧 出货线配置验证:`);
      console.log(`   出货速度: 5秒`);
      console.log(`   方块单位: 5牛奶`);
      console.log(`   方块价格: 5GEM`);
      
      // 正确的计算
      const correctMilkPerSecond = 5 / 5; // 5牛奶 ÷ 5秒 = 1牛奶/秒
      const correctGemPerSecond = 5 / 5;  // 5GEM ÷ 5秒 = 1GEM/秒
      
      console.log(`📈 正确的计算应该是:`);
      console.log(`   牛奶消耗速率: ${correctMilkPerSecond} 牛奶/秒`);
      console.log(`   GEM生产速率: ${correctGemPerSecond} GEM/秒`);
      
      // 验证修复
      console.log(`🔍 修复验证:`);
      if (Math.abs(rates.gemPerSecond - correctGemPerSecond) < 0.1) {
        console.log(`   ✅ GEM生产速率计算正确 (${rates.gemPerSecond} ≈ ${correctGemPerSecond})`);
      } else {
        console.log(`   ❌ GEM生产速率计算错误 (${rates.gemPerSecond} ≠ ${correctGemPerSecond})`);
        console.log(`   💡 可能的问题: gemPerSecond = deliveryProcessingPerSecond * blockPrice (错误)`);
        console.log(`   💡 应该是: gemPerSecond = blockPrice / deliverySpeed (正确)`);
      }
      
      // 计算理论值
      const timeSeconds = rates.timeElapsedSeconds;
      const expectedGemProduction = correctGemPerSecond * timeSeconds;
      const expectedMilkConsumption = correctMilkPerSecond * timeSeconds;
      
      console.log(`📊 基于正确速率的理论计算:`);
      console.log(`   理论GEM生产: ${expectedGemProduction.toFixed(3)}`);
      console.log(`   理论牛奶消耗: ${expectedMilkConsumption.toFixed(3)}`);
      
      // 分析实际返回值
      if (data.changes?.details) {
        const details = data.changes.details;
        console.log(`📋 API返回的实际值:`);
        console.log(`   GEM增加: ${details.gem?.increased || 0}`);
        console.log(`   牛奶增加: ${details.milk?.increased || 0}`);
        console.log(`   牛奶减少: ${details.milk?.decreased || 0}`);
        
        // 验证GEM计算
        if (details.gem?.increased) {
          const gemIncreased = details.gem.increased;
          console.log(`💎 GEM验证:`);
          
          // 检查是否使用了正确的计算
          if (Math.abs(gemIncreased - expectedGemProduction) < 0.1) {
            console.log(`   ✅ GEM增量基于正确的速率计算`);
          } else {
            console.log(`   ⚠️  GEM增量可能基于错误的速率或使用了前端请求值`);
          }
          
          // 检查1.5倍验证逻辑
          const maxAllowed = expectedGemProduction * 1.5;
          const requestedGem = requestData.tempGem || 0;
          
          if (requestedGem <= maxAllowed) {
            console.log(`   📝 前端请求 ${requestedGem} ≤ 最大允许 ${maxAllowed.toFixed(3)}，应使用前端值`);
            if (gemIncreased === requestedGem) {
              console.log(`   ✅ 正确使用了前端请求值`);
            } else {
              console.log(`   ❌ 应该使用前端请求值但没有使用`);
            }
          } else {
            console.log(`   📝 前端请求 ${requestedGem} > 最大允许 ${maxAllowed.toFixed(3)}，应使用理论值`);
            if (Math.abs(gemIncreased - expectedGemProduction) < 0.1) {
              console.log(`   ✅ 正确使用了理论计算值`);
            } else {
              console.log(`   ❌ 应该使用理论值但没有使用`);
            }
          }
        }
      }
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testGemCalculationFix() {
  console.log('🧪 开始测试GEM计算修复...\n');
  console.log('🔧 出货线配置:');
  console.log('   出货速度: 5秒');
  console.log('   方块单位: 5牛奶');
  console.log('   方块价格: 5GEM');
  console.log('   正确的GEM生产速率应该是: 5GEM ÷ 5秒 = 1GEM/秒');

  try {
    // 1. 测试合理的GEM请求
    console.log('\n📋 测试1: 合理的GEM请求');
    const request1 = {
      tempGem: 10
    };
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeGemCalculation(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试你的原始请求
    console.log('\n📋 测试2: 你的原始请求');
    const request2 = {
      tempGem: 100,
      tempMilk: {
        add: 100,
        reduce: 100
      }
    };
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeGemCalculation(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒避免防刷保护
    console.log('\n⏳ 等待6秒避免防刷保护...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试边界情况
    console.log('\n📋 测试3: 边界情况（可能超出1.5倍范围）');
    const request3 = {
      tempGem: 200
    };
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeGemCalculation(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 期望的修复效果：');
  console.log('✅ gemPerSecond 应该显示 1 而不是 5');
  console.log('✅ 计算公式: gemPerSecond = blockPrice / deliverySpeed');
  console.log('✅ 而不是: gemPerSecond = deliveryProcessingPerSecond * blockPrice');
}

// 运行测试
testGemCalculationFix();
