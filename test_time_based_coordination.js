// 测试基于时间的协调计算
// 验证当农场生产慢时，出货线等待农场生产的逻辑

console.log('🧪 测试基于时间的协调计算');

// 模拟农场和出货线数据
const mockFarmPlots = [
  { productionSpeed: 10, milkProduction: 5 }, // 每10秒产5牛奶
  { productionSpeed: 15, milkProduction: 8 }  // 每15秒产8牛奶
];

const mockDeliveryLine = {
  deliverySpeed: 5,    // 每5秒处理一次
  blockUnit: 10,       // 每次消耗10牛奶
  blockPrice: 2        // 每次产出2GEM
};

// 模拟协调计算函数
function calculateTimeBasedCoordination(farmPlots, deliveryLine, timeElapsedSeconds, initialPendingMilk) {
  let currentPendingMilk = initialPendingMilk;
  let totalFarmProduced = 0;
  let totalDeliveryConsumed = 0;
  let totalGemProduced = 0;

  // 创建事件队列
  const events = [];

  // 添加农场生产事件
  farmPlots.forEach((plot, index) => {
    let nextProductionTime = plot.productionSpeed;
    while (nextProductionTime <= timeElapsedSeconds) {
      events.push({
        time: nextProductionTime,
        type: 'farm',
        plotIndex: index,
        amount: plot.milkProduction
      });
      nextProductionTime += plot.productionSpeed;
    }
  });

  // 添加出货线处理事件
  let nextDeliveryTime = deliveryLine.deliverySpeed;
  while (nextDeliveryTime <= timeElapsedSeconds) {
    events.push({
      time: nextDeliveryTime,
      type: 'delivery',
      amount: deliveryLine.blockUnit
    });
    nextDeliveryTime += deliveryLine.deliverySpeed;
  }

  // 按时间排序事件
  events.sort((a, b) => a.time - b.time);

  console.log('\n📅 事件时间线：');
  events.forEach(event => {
    console.log(`时间 ${event.time}s: ${event.type === 'farm' ? '农场生产' : '出货线处理'} ${event.amount} ${event.type === 'farm' ? '牛奶' : '牛奶/GEM'}`);
  });

  console.log('\n🔄 处理过程：');
  console.log(`初始库存: ${currentPendingMilk} 牛奶`);

  // 处理事件队列
  for (const event of events) {
    if (event.type === 'farm') {
      // 农场生产事件
      currentPendingMilk += event.amount;
      totalFarmProduced += event.amount;
      console.log(`时间 ${event.time}s: 农场${event.plotIndex + 1}生产 ${event.amount} 牛奶，库存: ${currentPendingMilk}`);
    } else if (event.type === 'delivery') {
      // 出货线处理事件
      const requiredMilk = event.amount;
      
      if (currentPendingMilk >= requiredMilk) {
        // 有足够牛奶，可以处理
        currentPendingMilk -= requiredMilk;
        totalDeliveryConsumed += requiredMilk;
        totalGemProduced += deliveryLine.blockPrice;
        console.log(`时间 ${event.time}s: 出货线消耗 ${requiredMilk} 牛奶，产出 ${deliveryLine.blockPrice} GEM，库存: ${currentPendingMilk}`);
      } else {
        // 牛奶不够，出货线等待
        console.log(`时间 ${event.time}s: 出货线等待（需要 ${requiredMilk} 牛奶，但只有 ${currentPendingMilk}），库存: ${currentPendingMilk}`);
      }
    }
  }

  return {
    farmProduced: totalFarmProduced,
    deliveryConsumed: totalDeliveryConsumed,
    gemProduced: totalGemProduced,
    finalPendingMilk: currentPendingMilk
  };
}

// 测试场景1：农场生产慢，出货线等待
console.log('\n📋 测试场景1：农场生产慢，出货线需要等待');
const result1 = calculateTimeBasedCoordination(mockFarmPlots, mockDeliveryLine, 30, 0);
console.log('\n📊 结果：');
console.log(`农场总产量: ${result1.farmProduced} 牛奶`);
console.log(`出货线消耗: ${result1.deliveryConsumed} 牛奶`);
console.log(`GEM产出: ${result1.gemProduced} GEM`);
console.log(`最终库存: ${result1.finalPendingMilk} 牛奶`);

// 测试场景2：有初始库存的情况
console.log('\n📋 测试场景2：有初始库存的情况');
const result2 = calculateTimeBasedCoordination(mockFarmPlots, mockDeliveryLine, 30, 20);
console.log('\n📊 结果：');
console.log(`农场总产量: ${result2.farmProduced} 牛奶`);
console.log(`出货线消耗: ${result2.deliveryConsumed} 牛奶`);
console.log(`GEM产出: ${result2.gemProduced} GEM`);
console.log(`最终库存: ${result2.finalPendingMilk} 牛奶`);

// 对比传统计算方法
console.log('\n📋 对比传统计算方法：');
function traditionalCalculation(farmPlots, deliveryLine, timeElapsedSeconds, initialPendingMilk) {
  // 传统方法：独立计算农场和出货线
  let totalFarmProduced = 0;
  farmPlots.forEach(plot => {
    const cycles = Math.floor(timeElapsedSeconds / plot.productionSpeed);
    totalFarmProduced += cycles * plot.milkProduction;
  });

  const totalAvailableMilk = initialPendingMilk + totalFarmProduced;
  const deliveryCycles = Math.floor(timeElapsedSeconds / deliveryLine.deliverySpeed);
  const theoreticalDeliveryConsumption = deliveryCycles * deliveryLine.blockUnit;
  const actualDeliveryConsumption = Math.min(theoreticalDeliveryConsumption, totalAvailableMilk);
  const gemProduced = (actualDeliveryConsumption / deliveryLine.blockUnit) * deliveryLine.blockPrice;

  return {
    farmProduced: totalFarmProduced,
    deliveryConsumed: actualDeliveryConsumption,
    gemProduced: gemProduced,
    finalPendingMilk: totalAvailableMilk - actualDeliveryConsumption
  };
}

const traditionalResult = traditionalCalculation(mockFarmPlots, mockDeliveryLine, 30, 0);
console.log('传统方法结果：');
console.log(`农场总产量: ${traditionalResult.farmProduced} 牛奶`);
console.log(`出货线消耗: ${traditionalResult.deliveryConsumed} 牛奶`);
console.log(`GEM产出: ${traditionalResult.gemProduced} GEM`);
console.log(`最终库存: ${traditionalResult.finalPendingMilk} 牛奶`);

console.log('\n🎯 关键差异：');
console.log('1. 新方法考虑了时间依赖关系，出货线会等待农场生产');
console.log('2. 传统方法可能高估出货线的处理能力');
console.log('3. 新方法更准确地反映了游戏的实际机制');

console.log('\n✅ 基于时间的协调计算测试完成！');
