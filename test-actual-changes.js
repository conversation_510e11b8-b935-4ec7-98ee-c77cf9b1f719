const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3456';
const TEST_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.yugH7LmC26ly1v5D8z-DRHbyMd9R1-CQE76PDLCBF6E';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json',
    'Accept-Language': 'zh'
  }
});

// 分析实际变化的函数
function analyzeActualChanges(response, testName, requestData) {
  console.log(`\n📊 ${testName} - 实际变化量验证:`);
  console.log(`📝 请求参数:`, JSON.stringify(requestData, null, 2));
  
  if (response.data && response.data.data) {
    const data = response.data.data;
    
    // 分析时间信息
    const beforeTime = new Date(data.beforeUpdate?.lastActiveTime + ' UTC');
    const afterTime = new Date(data.afterUpdate?.lastActiveTime + ' UTC');
    const actualTimeDiff = (afterTime - beforeTime) / 1000;
    
    console.log(`⏰ 时间分析:`);
    console.log(`   实际时间差: ${actualTimeDiff.toFixed(3)} 秒`);
    
    // 分析实际资源变化
    const gemBefore = data.beforeUpdate?.gem || 0;
    const gemAfter = data.afterUpdate?.gem || 0;
    const actualGemChange = gemAfter - gemBefore;
    
    const milkBefore = data.beforeUpdate?.pendingMilk || 0;
    const milkAfter = data.afterUpdate?.pendingMilk || 0;
    const actualMilkChange = milkAfter - milkBefore;
    
    console.log(`💰 实际资源变化:`);
    console.log(`   GEM: ${gemBefore} → ${gemAfter} (变化: ${actualGemChange})`);
    console.log(`   牛奶: ${milkBefore} → ${milkAfter} (变化: ${actualMilkChange})`);
    
    // 分析details值
    if (data.changes?.details) {
      const details = data.changes.details;
      console.log(`📋 Details显示:`);
      console.log(`   gem.increased: ${details.gem?.increased || 0}`);
      console.log(`   milk.increased: ${details.milk?.increased || 0}`);
      console.log(`   milk.decreased: ${details.milk?.decreased || 0}`);
      
      // 验证details是否与实际变化匹配
      console.log(`🔍 匹配度验证:`);
      
      // 验证GEM
      if (Math.abs((details.gem?.increased || 0) - Math.max(0, actualGemChange)) < 0.1) {
        console.log(`   ✅ GEM匹配: details(${details.gem?.increased}) = 实际变化(${actualGemChange})`);
      } else {
        console.log(`   ❌ GEM不匹配: details(${details.gem?.increased}) ≠ 实际变化(${actualGemChange})`);
      }
      
      // 验证牛奶
      const detailsMilkNet = (details.milk?.increased || 0) - (details.milk?.decreased || 0);
      if (Math.abs(detailsMilkNet - actualMilkChange) < 0.1) {
        console.log(`   ✅ 牛奶匹配: details净值(${detailsMilkNet}) = 实际变化(${actualMilkChange})`);
      } else {
        console.log(`   ❌ 牛奶不匹配: details净值(${detailsMilkNet}) ≠ 实际变化(${actualMilkChange})`);
        console.log(`   💡 这可能表示details显示的是理论值而不是实际值`);
      }
      
      // 前端同步验证
      console.log(`📱 前端同步验证:`);
      const frontendGemAfterSync = gemBefore + (details.gem?.increased || 0);
      const frontendMilkAfterSync = milkBefore + (details.milk?.increased || 0) - (details.milk?.decreased || 0);
      
      if (Math.abs(frontendGemAfterSync - gemAfter) < 0.1) {
        console.log(`   ✅ GEM可同步: ${gemBefore} + ${details.gem?.increased} = ${frontendGemAfterSync} ≈ ${gemAfter}`);
      } else {
        console.log(`   ❌ GEM无法同步: ${gemBefore} + ${details.gem?.increased} = ${frontendGemAfterSync} ≠ ${gemAfter}`);
      }
      
      if (Math.abs(frontendMilkAfterSync - milkAfter) < 0.1) {
        console.log(`   ✅ 牛奶可同步: ${milkBefore} + ${details.milk?.increased} - ${details.milk?.decreased} = ${frontendMilkAfterSync} ≈ ${milkAfter}`);
      } else {
        console.log(`   ❌ 牛奶无法同步: ${milkBefore} + ${details.milk?.increased} - ${details.milk?.decreased} = ${frontendMilkAfterSync} ≠ ${milkAfter}`);
      }
    }
    
    // 分析游戏逻辑合理性
    console.log(`🎮 游戏逻辑分析:`);
    const farmCycles = Math.floor(actualTimeDiff / 5);
    const deliveryCycles = Math.floor(actualTimeDiff / 5);
    
    console.log(`   农场周期数: ${farmCycles} (理论产量: ${farmCycles * 1}牛奶)`);
    console.log(`   出货线周期数: ${deliveryCycles} (理论消耗: ${deliveryCycles * 5}牛奶, 理论产出: ${deliveryCycles * 5}GEM)`);
    
    // 检查是否符合协调逻辑
    const theoreticalMilkProduction = farmCycles * 1;
    const theoreticalMilkConsumption = deliveryCycles * 5;
    const availableMilk = milkBefore + theoreticalMilkProduction;
    const actualPossibleConsumption = Math.min(theoreticalMilkConsumption, availableMilk);
    const expectedFinalMilk = availableMilk - actualPossibleConsumption;
    
    console.log(`   可用牛奶: ${milkBefore} + ${theoreticalMilkProduction} = ${availableMilk}`);
    console.log(`   实际可消耗: min(${theoreticalMilkConsumption}, ${availableMilk}) = ${actualPossibleConsumption}`);
    console.log(`   期望最终库存: ${availableMilk} - ${actualPossibleConsumption} = ${expectedFinalMilk}`);
    
    if (Math.abs(milkAfter - expectedFinalMilk) < 0.1) {
      console.log(`   ✅ 符合协调逻辑: 实际库存(${milkAfter}) ≈ 期望库存(${expectedFinalMilk})`);
    } else {
      console.log(`   ❌ 不符合协调逻辑: 实际库存(${milkAfter}) ≠ 期望库存(${expectedFinalMilk})`);
    }
    
  } else {
    console.log(`❌ 响应数据结构异常`);
  }
}

// 测试函数
async function testActualChanges() {
  console.log('🧪 开始测试实际变化量显示...\n');
  console.log('🎯 测试目标:');
  console.log('1. details显示实际执行的变化量');
  console.log('2. 前端可以直接用details同步资源数量');
  console.log('3. details与实际资源变化匹配');
  console.log('4. 符合协调生产逻辑');

  try {
    // 1. 测试无参数请求
    console.log('\n📋 测试1: 无参数请求（系统自动计算）');
    const request1 = {};
    
    try {
      const response1 = await api.post('/api/wallet/batch-update-resources', request1);
      console.log('✅ 请求成功');
      analyzeActualChanges(response1, '测试1', request1);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 2. 测试合理的前端请求
    console.log('\n📋 测试2: 合理的前端请求');
    const request2 = {
      gemRequest: 3,
      milkOperations: {
        produce: 1,
        consume: 2
      }
    };
    
    try {
      const response2 = await api.post('/api/wallet/batch-update-resources', request2);
      console.log('✅ 请求成功');
      analyzeActualChanges(response2, '测试2', request2);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

    // 等待6秒
    console.log('\n⏳ 等待6秒...');
    await new Promise(resolve => setTimeout(resolve, 6000));

    // 3. 测试超出范围的请求
    console.log('\n📋 测试3: 超出范围的请求');
    const request3 = {
      gemRequest: 100,
      milkOperations: {
        produce: 50,
        consume: 50
      }
    };
    
    try {
      const response3 = await api.post('/api/wallet/batch-update-resources', request3);
      console.log('✅ 请求成功');
      analyzeActualChanges(response3, '测试3', request3);
    } catch (error) {
      console.log('❌ 请求失败:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🎯 测试完成！');
  console.log('\n📝 期望的修复效果：');
  console.log('✅ details.gem.increased = 实际GEM增加量');
  console.log('✅ details.milk.increased - details.milk.decreased = 实际牛奶净变化');
  console.log('✅ 前端可以用details直接同步资源数量');
  console.log('✅ 不会出现"理论消耗"与"实际库存"不匹配的情况');
}

// 运行测试
testActualChanges();
