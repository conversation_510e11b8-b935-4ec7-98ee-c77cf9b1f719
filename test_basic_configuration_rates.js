// 测试基础配置信息的 productionRates 字段
// 验证从实际生产量改为基础配置参数的效果

console.log('🧪 测试基础配置信息的 productionRates 字段');

// 模拟用户的农场和出货线配置
const userConfiguration = {
  farmPlots: [
    { plotNumber: 1, level: 3, milkProduction: 2.25, productionSpeed: 4.535, isUnlocked: true },
    { plotNumber: 2, level: 2, milkProduction: 3.00, productionSpeed: 4.762, isUnlocked: true },
    { plotNumber: 3, level: 1, milkProduction: 4.00, productionSpeed: 5.000, isUnlocked: false }
  ],
  deliveryLine: {
    level: 2,
    deliverySpeed: 4.95,  // 每4.95秒处理一次
    blockUnit: 10,        // 每个方块消耗10牛奶
    blockPrice: 10        // 每个方块产出10GEM
  }
};

console.log('\n📊 用户配置信息：');
console.log('农场区配置：');
userConfiguration.farmPlots.forEach(plot => {
  console.log(`  农场区${plot.plotNumber}: 等级${plot.level}, 产量${plot.milkProduction}牛奶/周期, 速度${plot.productionSpeed}秒/周期, ${plot.isUnlocked ? '已解锁' : '未解锁'}`);
});

console.log('\n出货线配置：');
console.log(`  等级${userConfiguration.deliveryLine.level}: 速度${userConfiguration.deliveryLine.deliverySpeed}秒/次, 消耗${userConfiguration.deliveryLine.blockUnit}牛奶/方块, 价格${userConfiguration.deliveryLine.blockPrice}GEM/方块`);

// 模拟基础配置计算函数
function calculateBasicConfigurationRates(farmPlots, deliveryLine, timeElapsedSeconds) {
  // 计算农场每个周期的总产量（只计算已解锁的农场区）
  let totalFarmMilkPerCycle = 0;
  farmPlots.forEach(plot => {
    if (plot.isUnlocked) {
      totalFarmMilkPerCycle += plot.milkProduction;
    }
  });

  return {
    farmMilkPerCycle: parseFloat(totalFarmMilkPerCycle.toFixed(3)),
    deliveryBlockUnit: deliveryLine.blockUnit,
    deliveryBlockPrice: deliveryLine.blockPrice,
    timeElapsedSeconds: timeElapsedSeconds
  };
}

const basicRates = calculateBasicConfigurationRates(
  userConfiguration.farmPlots,
  userConfiguration.deliveryLine,
  9.574
);

console.log('\n⚙️ 基础配置计算结果：');
console.log(`farmMilkPerCycle: ${basicRates.farmMilkPerCycle} 牛奶/周期`);
console.log(`deliveryBlockUnit: ${basicRates.deliveryBlockUnit} 牛奶/方块`);
console.log(`deliveryBlockPrice: ${basicRates.deliveryBlockPrice} GEM/方块`);
console.log(`timeElapsedSeconds: ${basicRates.timeElapsedSeconds} 秒`);

// 对比修改前后的API响应格式
console.log('\n🔄 API响应格式对比：');

console.log('\n修改前（实际生产量）：');
const oldResponse = {
  "productionRates": {
    "farmMilkProduced": 7.595,        // 在这个时间段内的实际生产量
    "deliveryMilkConsumed": 5.000,    // 在这个时间段内的实际消耗量
    "gemProduced": 5,                 // 在这个时间段内的实际GEM产量
    "timeElapsedSeconds": 9.574
  }
};
console.log(JSON.stringify(oldResponse, null, 2));

console.log('\n修改后（基础配置信息）：');
const newResponse = {
  "productionRates": {
    "farmMilkPerCycle": basicRates.farmMilkPerCycle,      // 农场每个生产周期的牛奶产量
    "deliveryBlockUnit": basicRates.deliveryBlockUnit,    // 出货线每个方块消耗的牛奶数量
    "deliveryBlockPrice": basicRates.deliveryBlockPrice,  // 出货线每个方块的GEM价格
    "timeElapsedSeconds": basicRates.timeElapsedSeconds
  }
};
console.log(JSON.stringify(newResponse, null, 2));

console.log('\n🎯 字段含义对比：');

console.log('\n修改前的字段含义：');
console.log('  - farmMilkProduced: 农场在此时间段内的实际生产量');
console.log('  - deliveryMilkConsumed: 出货线在此时间段内的实际消耗量');
console.log('  - gemProduced: 在此时间段内的实际GEM产量');
console.log('  → 这些值受时间间隔影响，反映实际发生的数量');

console.log('\n修改后的字段含义：');
console.log('  - farmMilkPerCycle: 农场每个生产周期的牛奶产量（基础配置）');
console.log('  - deliveryBlockUnit: 出货线每个方块消耗的牛奶数量（基础配置）');
console.log('  - deliveryBlockPrice: 出货线每个方块的GEM价格（基础配置）');
console.log('  → 这些值不受时间间隔影响，反映游戏机制的基础参数');

console.log('\n✅ 修改的优势：');
console.log('1. 稳定性：不受时间间隔影响，即使"Too soon since last update"也能正确显示');
console.log('2. 直观性：用户可以直接看到当前的游戏配置参数');
console.log('3. 实用性：帮助用户了解升级效果和游戏机制');
console.log('4. 一致性：无论何时调用API，这些基础配置信息都是一致的');

console.log('\n📈 使用场景：');
console.log('1. 用户查看当前农场和出货线的基础能力');
console.log('2. 计算升级后的效果预览');
console.log('3. 了解游戏机制的核心参数');
console.log('4. 前端显示配置信息，不需要额外的API调用');

console.log('\n🔍 计算逻辑：');
console.log('- farmMilkPerCycle = 所有已解锁农场区的 milkProduction 之和');
console.log('- deliveryBlockUnit = 出货线的 blockUnit 配置');
console.log('- deliveryBlockPrice = 出货线的 blockPrice 配置');
console.log('- timeElapsedSeconds = 实际经过的时间（保持不变）');

console.log('\n💡 前端使用建议：');
console.log('1. 使用 farmMilkPerCycle 显示"农场每周期产量"');
console.log('2. 使用 deliveryBlockUnit 显示"每方块牛奶消耗"');
console.log('3. 使用 deliveryBlockPrice 显示"每方块GEM收益"');
console.log('4. 这些值可以用于计算理论的生产效率和收益率');

console.log('\n✅ 基础配置信息的 productionRates 字段测试完成！');
